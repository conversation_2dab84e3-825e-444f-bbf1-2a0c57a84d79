# 事件数据渲染功能使用指南

## 🎯 功能概述

完善了通过事件获取后端数据并渲染到页面的功能，支持：

1. **API事件调用** - 通过按钮点击等事件调用后端API
2. **数据绑定** - 将API返回的数据绑定到页面组件
3. **动态渲染** - 组件根据数据变化自动更新显示
4. **数据管理** - 统一管理页面数据状态

## 🔧 核心组件

### 1. 数据管理器 (`dataManager.js`)
- 统一管理页面数据、API响应、变量状态
- 支持数据绑定表达式解析
- 提供API请求执行功能

### 2. 事件处理器 (`EventHandlerWrapper.vue`)
- 增强API事件处理，支持数据绑定配置
- 自动将API响应数据存储到数据管理器
- 触发数据更新事件通知组件刷新

### 3. 文本组件 (`TextComponent.vue`)
- 支持数据绑定表达式显示
- 自动响应数据更新事件
- 支持对象数据的JSON格式化显示

### 4. 数据展示组件 (`DataDisplayComponent.vue`)
- 专门用于展示API返回的数据
- 支持列表、对象、简单数据的不同展示模式
- 包含加载状态、错误处理、空数据提示

## 🚀 使用方法

### 步骤1: 配置API事件

1. **选择触发组件**（如按钮）
2. **添加点击事件**
3. **配置API调用**：
   - API URL: `http://localhost:8080/api/images/list`
   - 请求方法: `GET`
   - 数据键名: `imageList` （用于标识数据）
   - 结果变量名: `apiResult` （可选）

### 步骤2: 配置数据绑定

在API事件配置中设置：
- **数据绑定目标**: 选择要显示数据的组件
- **属性名**: `data` 或其他属性

### 步骤3: 设置显示组件

#### 方式1: 使用文本组件
在文本组件的内容中使用数据绑定表达式：
```
{{apiData.imageList}}
```

#### 方式2: 使用数据展示组件
1. 添加数据展示组件到页面
2. 设置数据源: `{{apiData.imageList}}`
3. 配置显示模式（列表/对象/简单）

## 📝 数据绑定表达式

### API数据引用
```javascript
{{apiData.imageList}}        // 引用API返回的数据
{{apiData.userInfo}}         // 引用用户信息API数据
```

### 变量引用
```javascript
{{variables.userName}}       // 引用变量
{{variables.currentPage}}    // 引用当前页码变量
{{userName}}                 // 简化写法
```

### 页面数据引用
```javascript
{{pageData.selectedItem}}    // 引用页面数据
```

## 🎨 完整示例

### 示例1: 图片列表展示

1. **添加按钮组件**
   - 文本: "加载图片列表"
   - 添加点击事件

2. **配置API事件**
   ```json
   {
     "type": "click",
     "action": {
       "type": "api",
       "apiUrl": "http://localhost:8080/api/images/list",
       "apiMethod": "get",
       "dataKey": "imageList",
       "showSuccessMessage": true
     }
   }
   ```

3. **添加数据展示组件**
   - 数据源: `{{apiData.imageList}}`
   - 显示模式: "card"
   - 显示标题: true
   - 标题: "图片列表"

### 示例2: 用户信息查询

1. **添加输入框组件**
   - 占位符: "请输入用户ID"
   - 变量绑定: `userId`

2. **添加查询按钮**
   - 文本: "查询用户"
   - API事件配置:
   ```json
   {
     "apiUrl": "http://localhost:8080/api/users/{{variables.userId}}",
     "apiMethod": "get",
     "dataKey": "userInfo"
   }
   ```

3. **添加文本组件显示结果**
   - 内容: `{{apiData.userInfo}}`

## 🔄 事件流程

```mermaid
graph TD
    A[用户点击按钮] --> B[触发API事件]
    B --> C[数据管理器执行API请求]
    C --> D[存储API响应数据]
    D --> E[触发数据更新事件]
    E --> F[组件接收事件并刷新显示]
    F --> G[页面显示最新数据]
```

## 📊 数据状态管理

### 数据存储结构
```javascript
{
  // API响应数据
  apiData: {
    imageList: [...],
    userInfo: {...},
    orderList: [...]
  },
  
  // 页面变量
  variables: {
    userName: "张三",
    currentPage: 1,
    selectedId: 123
  },
  
  // 页面数据
  pageData: {
    selectedItem: {...},
    formData: {...}
  },
  
  // 加载状态
  loading: {
    imageList: false,
    userInfo: true
  },
  
  // 错误状态
  errors: {
    imageList: null,
    userInfo: "网络错误"
  }
}
```

## 🛠️ 高级功能

### 1. 条件数据绑定
```javascript
// 根据条件显示不同数据
{{variables.showDetail ? apiData.detailInfo : apiData.summaryInfo}}
```

### 2. 数据格式化
组件会自动处理：
- 对象数据 → JSON格式化显示
- 数组数据 → 列表展示
- 布尔值 → "是"/"否"
- 空值 → "-"

### 3. 错误处理
- API请求失败时显示错误信息
- 数据为空时显示空状态提示
- 加载过程中显示加载动画

### 4. 事件链式调用
```javascript
// 第一个API调用完成后触发第二个API
{
  "apiUrl": "/api/users/list",
  "dataKey": "userList",
  "onSuccess": {
    "type": "api",
    "apiUrl": "/api/users/{{apiData.userList[0].id}}/detail",
    "dataKey": "userDetail"
  }
}
```

## 🎯 最佳实践

### 1. 数据键名规范
- 使用有意义的名称: `userList`, `orderDetail`
- 避免重复: 每个API使用唯一的数据键名
- 保持一致: 相同类型的数据使用相似的命名

### 2. 错误处理
- 为每个API配置错误消息
- 在数据展示组件中设置空数据提示
- 使用加载状态提升用户体验

### 3. 性能优化
- 避免频繁的API调用
- 使用数据缓存减少重复请求
- 合理使用数据绑定，避免循环引用

### 4. 调试技巧
- 打开浏览器控制台查看数据管理器日志
- 使用 `dataManager.getState()` 查看当前数据状态
- 检查事件触发和数据更新的时序

通过这套完整的事件数据渲染系统，您可以轻松实现从后端获取数据并动态渲染到页面的功能，大大提升低代码平台的数据处理能力！🎉
