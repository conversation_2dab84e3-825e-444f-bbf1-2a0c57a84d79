/**
 * 页面数据管理器
 * 用于管理页面级别的数据状态和API响应数据
 */

import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'

class DataManager {
  constructor() {
    // 页面数据存储
    this.pageData = reactive({})
    
    // API响应数据存储
    this.apiData = reactive({})
    
    // 变量存储
    this.variables = reactive({})
    
    // 加载状态
    this.loading = reactive({})
    
    // 错误状态
    this.errors = reactive({})
  }

  /**
   * 设置页面数据
   * @param {string} key 数据键
   * @param {any} value 数据值
   */
  setPageData(key, value) {
    console.log(`DataManager: Setting page data ${key}:`, value)
    this.pageData[key] = value
  }

  /**
   * 获取页面数据
   * @param {string} key 数据键
   * @returns {any} 数据值
   */
  getPageData(key) {
    return this.pageData[key]
  }

  /**
   * 设置API响应数据
   * @param {string} apiKey API标识
   * @param {any} data 响应数据
   */
  setApiData(apiKey, data) {
    console.log(`DataManager: Setting API data ${apiKey}:`, data)
    this.apiData[apiKey] = data
    
    // 触发数据更新事件
    this.notifyDataUpdate(apiKey, data)
  }

  /**
   * 获取API响应数据
   * @param {string} apiKey API标识
   * @returns {any} 响应数据
   */
  getApiData(apiKey) {
    return this.apiData[apiKey]
  }

  /**
   * 设置变量
   * @param {string} name 变量名
   * @param {any} value 变量值
   */
  setVariable(name, value) {
    console.log(`DataManager: Setting variable ${name}:`, value)
    this.variables[name] = value
  }

  /**
   * 获取变量
   * @param {string} name 变量名
   * @returns {any} 变量值
   */
  getVariable(name) {
    return this.variables[name]
  }

  /**
   * 设置加载状态
   * @param {string} key 状态键
   * @param {boolean} isLoading 是否加载中
   */
  setLoading(key, isLoading) {
    this.loading[key] = isLoading
  }

  /**
   * 获取加载状态
   * @param {string} key 状态键
   * @returns {boolean} 是否加载中
   */
  isLoading(key) {
    return this.loading[key] || false
  }

  /**
   * 设置错误状态
   * @param {string} key 错误键
   * @param {any} error 错误信息
   */
  setError(key, error) {
    this.errors[key] = error
  }

  /**
   * 获取错误状态
   * @param {string} key 错误键
   * @returns {any} 错误信息
   */
  getError(key) {
    return this.errors[key]
  }

  /**
   * 清除错误状态
   * @param {string} key 错误键
   */
  clearError(key) {
    delete this.errors[key]
  }

  /**
   * 执行API请求
   * @param {Object} config API配置
   * @returns {Promise} 请求Promise
   */
  async executeApi(config) {
    const { url, method = 'GET', params = {}, headers = {}, dataKey } = config
    const apiKey = dataKey || `api_${Date.now()}`
    
    console.log(`DataManager: Executing API ${method} ${url}`, { params, headers })
    
    // 设置加载状态
    this.setLoading(apiKey, true)
    this.clearError(apiKey)
    
    try {
      // 构建请求配置
      const requestConfig = {
        method: method.toUpperCase(),
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      }
      
      // 处理请求参数
      if (method.toLowerCase() === 'get') {
        // GET请求将参数添加到URL
        const urlParams = new URLSearchParams(params)
        const separator = url.includes('?') ? '&' : '?'
        const fullUrl = Object.keys(params).length > 0 ? `${url}${separator}${urlParams}` : url
        
        const response = await fetch(fullUrl, requestConfig)
        const data = await response.json()

        if (!response.ok) {
          throw new Error(data?.message || `HTTP ${response.status}`)
        }
        
        // 存储响应数据
        this.setApiData(apiKey, data)
        
        console.log(`DataManager: API ${apiKey} success:`, data)
        return { success: true, data, apiKey }
        
      } else {
        // POST/PUT/DELETE请求将参数放在请求体中
        requestConfig.body = JSON.stringify(params)
        
        const response = await fetch(url, requestConfig)
        const data = await response.json()

        if (!response.ok) {
          throw new Error(data?.message || `HTTP ${response.status}`)
        }
        
        // 存储响应数据
        this.setApiData(apiKey, data)
        
        console.log(`DataManager: API ${apiKey} success:`, data)
        return { success: true, data, apiKey }
      }
      
    } catch (error) {
      console.error(`DataManager: API ${apiKey} failed:`, error)
      const errorMessage = error?.message || error?.toString() || '未知错误'
      this.setError(apiKey, errorMessage)

      return { success: false, error: errorMessage, apiKey }

    } finally {
      this.setLoading(apiKey, false)
    }
  }

  /**
   * 通知数据更新
   * @param {string} key 数据键
   * @param {any} data 数据
   */
  notifyDataUpdate(key, data) {
    // 发送自定义事件，通知组件数据已更新
    const event = new CustomEvent('dataUpdate', {
      detail: { key, data }
    })
    document.dispatchEvent(event)
  }

  /**
   * 解析数据绑定表达式
   * @param {string} expression 绑定表达式，如 "{{apiData.userList}}" 或 "{{variables.userName}}"
   * @returns {any} 解析后的数据
   */
  parseDataBinding(expression) {
    if (!expression || typeof expression !== 'string') {
      return expression
    }
    
    // 检查是否是数据绑定表达式
    const bindingMatch = expression.match(/\{\{(.+?)\}\}/)
    if (!bindingMatch) {
      return expression
    }
    
    const bindingPath = bindingMatch[1].trim()
    console.log(`DataManager: Parsing data binding: ${bindingPath}`)
    
    try {
      // 解析绑定路径
      if (bindingPath.startsWith('apiData.')) {
        const apiKey = bindingPath.substring(8) // 移除 "apiData."
        return this.getApiData(apiKey)
      } else if (bindingPath.startsWith('variables.')) {
        const varName = bindingPath.substring(10) // 移除 "variables."
        return this.getVariable(varName)
      } else if (bindingPath.startsWith('pageData.')) {
        const dataKey = bindingPath.substring(9) // 移除 "pageData."
        return this.getPageData(dataKey)
      } else {
        // 直接从变量中查找
        return this.getVariable(bindingPath)
      }
    } catch (error) {
      console.error(`DataManager: Failed to parse data binding ${bindingPath}:`, error)
      return expression
    }
  }

  /**
   * 清除所有数据
   */
  clearAll() {
    Object.keys(this.pageData).forEach(key => delete this.pageData[key])
    Object.keys(this.apiData).forEach(key => delete this.apiData[key])
    Object.keys(this.variables).forEach(key => delete this.variables[key])
    Object.keys(this.loading).forEach(key => delete this.loading[key])
    Object.keys(this.errors).forEach(key => delete this.errors[key])
  }

  /**
   * 获取所有数据状态
   * @returns {Object} 数据状态
   */
  getState() {
    return {
      pageData: { ...this.pageData },
      apiData: { ...this.apiData },
      variables: { ...this.variables },
      loading: { ...this.loading },
      errors: { ...this.errors }
    }
  }
}

// 创建全局数据管理器实例
export const dataManager = new DataManager()

// 导出类以便创建新实例
export default DataManager
