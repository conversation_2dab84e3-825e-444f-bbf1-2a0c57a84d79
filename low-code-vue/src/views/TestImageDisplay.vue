<template>
  <div class="test-image-display">
    <el-card header="图片显示测试页面">
      <div class="test-sections">
        <!-- 测试1: 直接图片显示 -->
        <div class="test-section">
          <h3>测试1: 直接图片显示</h3>
          <div class="test-content">
            <img :src="testImageUrl" alt="测试图片" style="width: 200px; height: 150px; object-fit: cover;" />
            <p>图片URL: {{ testImageUrl }}</p>
          </div>
        </div>

        <!-- 测试2: 背景图片显示 -->
        <div class="test-section">
          <h3>测试2: 背景图片显示</h3>
          <div 
            class="background-test" 
            :style="{ 
              backgroundImage: `url(${testImageUrl})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }"
          >
            <p>这是背景图片测试</p>
          </div>
        </div>

        <!-- 测试3: 图片组件测试 -->
        <div class="test-section">
          <h3>测试3: 图片组件测试</h3>
          <div class="component-test">
            <image-component :component="testImageComponent" />
          </div>
        </div>

        <!-- 测试4: 带背景的组件测试 -->
        <div class="test-section">
          <h3>测试4: 带背景的组件测试</h3>
          <div class="component-test">
            <component-renderer :component="testBackgroundComponent" />
          </div>
        </div>

        <!-- 测试5: 页面背景测试 -->
        <div class="test-section">
          <h3>测试5: 页面背景测试</h3>
          <div 
            class="page-background-test"
            :style="pageBackgroundStyles"
          >
            <p>这是页面背景测试</p>
            <p>背景应该显示图片</p>
          </div>
        </div>

        <!-- 控制面板 -->
        <div class="test-section">
          <h3>控制面板</h3>
          <div class="controls">
            <el-form label-position="top">
              <el-form-item label="测试图片URL">
                <el-input v-model="testImageUrl" placeholder="输入图片URL" />
              </el-form-item>
              
              <el-form-item label="上传测试图片">
                <image-uploader v-model="testImageUrl" @update:modelValue="handleImageUpload" />
              </el-form-item>
              
              <el-form-item>
                <el-button @click="testImageLoad">测试图片加载</el-button>
                <el-button @click="resetTest">重置测试</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 调试信息 -->
        <div class="test-section">
          <h3>调试信息</h3>
          <div class="debug-info">
            <pre>{{ debugInfo }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import ImageComponent from '@/components/editor/components/ImageComponent.vue'
import ComponentRenderer from '@/components/editor/ComponentRenderer.vue'
import ImageUploader from '@/components/editor/ImageUploader.vue'

// 测试图片URL
const testImageUrl = ref('http://localhost:8080/api/files/uploads/2025/06/01/6986a2bad96942cab3233d9308901cf4.png')

// 测试图片组件
const testImageComponent = computed(() => ({
  id: 'test-image-1',
  type: 'image',
  props: {
    src: testImageUrl.value,
    alt: '测试图片'
  },
  styles: {
    width: '300px',
    height: '200px',
    border: '2px solid #409eff',
    borderRadius: '8px'
  }
}))

// 测试背景组件
const testBackgroundComponent = computed(() => ({
  id: 'test-background-1',
  type: 'text',
  props: {
    content: '这是带背景图片的文本组件'
  },
  styles: {
    width: '400px',
    height: '200px',
    backgroundImage: `url(${testImageUrl.value})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    color: 'white',
    fontSize: '18px',
    fontWeight: 'bold',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
    borderRadius: '8px'
  }
}))

// 页面背景样式
const pageBackgroundStyles = computed(() => ({
  width: '100%',
  height: '300px',
  backgroundImage: `url(${testImageUrl.value})`,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'white',
  fontSize: '16px',
  fontWeight: 'bold',
  textShadow: '2px 2px 4px rgba(0,0,0,0.7)',
  borderRadius: '8px',
  border: '2px solid #ddd'
}))

// 调试信息
const debugInfo = computed(() => ({
  testImageUrl: testImageUrl.value,
  imageComponent: testImageComponent.value,
  backgroundComponent: testBackgroundComponent.value,
  pageStyles: pageBackgroundStyles.value
}))

// 处理图片上传
const handleImageUpload = (url) => {
  console.log('图片上传成功，URL:', url)
  testImageUrl.value = url
  ElMessage.success('图片上传成功')
}

// 测试图片加载
const testImageLoad = () => {
  if (!testImageUrl.value) {
    ElMessage.warning('请先设置图片URL')
    return
  }
  
  const img = new Image()
  img.onload = () => {
    ElMessage.success('图片加载成功')
    console.log('图片尺寸:', img.width, 'x', img.height)
  }
  img.onerror = () => {
    ElMessage.error('图片加载失败')
    console.error('图片加载失败:', testImageUrl.value)
  }
  img.src = testImageUrl.value
}

// 重置测试
const resetTest = () => {
  testImageUrl.value = 'http://localhost:8080/api/files/uploads/2025/06/01/6986a2bad96942cab3233d9308901cf4.png'
  ElMessage.info('测试已重置')
}
</script>

<style scoped>
.test-image-display {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.test-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.test-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.background-test {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  border-radius: 8px;
  border: 2px solid #ddd;
}

.component-test {
  border: 2px dashed #409eff;
  padding: 20px;
  border-radius: 8px;
  background-color: white;
}

.page-background-test {
  border: 2px solid #409eff;
}

.controls {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.debug-info {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  max-height: 400px;
  overflow-y: auto;
}

.debug-info pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}
</style>
