<template>
  <div class="data-display-component" :style="componentStyles">
    <div v-if="isLoading" class="loading-state">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    
    <div v-else-if="hasError" class="error-state">
      <el-icon><Warning /></el-icon>
      <span>{{ errorMessage }}</span>
    </div>
    
    <div v-else-if="displayData" class="data-content">
      <!-- 列表数据展示 -->
      <div v-if="isArrayData" class="data-list">
        <div class="list-header" v-if="component.props.showHeader">
          <h4>{{ component.props.title || '数据列表' }}</h4>
          <span class="item-count">共 {{ displayData.length }} 项</span>
        </div>
        
        <div class="list-items">
          <div 
            v-for="(item, index) in displayData" 
            :key="index" 
            class="list-item"
            :class="{ 'clickable': component.props.itemClickable }"
            @click="handleItemClick(item, index)"
          >
            <div v-if="component.props.displayMode === 'card'" class="item-card">
              <div v-for="(value, key) in item" :key="key" class="item-field">
                <span class="field-label">{{ key }}:</span>
                <span class="field-value">{{ formatValue(value) }}</span>
              </div>
            </div>
            
            <div v-else-if="component.props.displayMode === 'table'" class="item-row">
              <span v-for="(value, key) in item" :key="key" class="item-cell">
                {{ formatValue(value) }}
              </span>
            </div>
            
            <div v-else class="item-simple">
              {{ formatValue(item) }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 对象数据展示 -->
      <div v-else-if="isObjectData" class="data-object">
        <div class="object-header" v-if="component.props.showHeader">
          <h4>{{ component.props.title || '数据详情' }}</h4>
        </div>
        
        <div class="object-fields">
          <div v-for="(value, key) in displayData" :key="key" class="object-field">
            <span class="field-label">{{ key }}:</span>
            <span class="field-value">{{ formatValue(value) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 简单数据展示 -->
      <div v-else class="data-simple">
        <div class="simple-header" v-if="component.props.showHeader">
          <h4>{{ component.props.title || '数据' }}</h4>
        </div>
        <div class="simple-value">{{ formatValue(displayData) }}</div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <el-icon><Document /></el-icon>
      <span>{{ component.props.emptyText || '暂无数据' }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onBeforeUnmount } from 'vue'
import { Loading, Warning, Document } from '@element-plus/icons-vue'
import { dataManager } from '../../../utils/dataManager'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update', 'itemClick'])

// 组件数据
const componentData = ref(null)
const isLoading = ref(false)
const errorMessage = ref('')

// 计算显示数据
const displayData = computed(() => {
  // 优先使用绑定的数据
  if (componentData.value !== null && componentData.value !== undefined) {
    return componentData.value
  }
  
  // 解析数据绑定表达式
  const dataSource = props.component.props?.dataSource
  if (dataSource) {
    const parsedData = dataManager.parseDataBinding(dataSource)
    if (parsedData !== dataSource) {
      return parsedData
    }
  }
  
  // 返回默认数据或null
  return props.component.props?.defaultData || null
})

// 判断是否为数组数据
const isArrayData = computed(() => {
  return Array.isArray(displayData.value)
})

// 判断是否为对象数据
const isObjectData = computed(() => {
  return displayData.value && 
         typeof displayData.value === 'object' && 
         !Array.isArray(displayData.value)
})

// 判断是否有错误
const hasError = computed(() => {
  return !!errorMessage.value
})

// 计算样式
const componentStyles = computed(() => {
  return props.component.styles || {}
})

// 格式化值显示
const formatValue = (value) => {
  if (value === null || value === undefined) {
    return '-'
  }
  
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  
  return String(value)
}

// 处理列表项点击
const handleItemClick = (item, index) => {
  if (props.component.props?.itemClickable) {
    console.log('DataDisplayComponent: Item clicked:', { item, index })
    
    // 发送点击事件
    emit('itemClick', { item, index, componentId: props.component.id })
    
    // 发送自定义事件
    document.dispatchEvent(new CustomEvent('dataItemClick', {
      detail: {
        componentId: props.component.id,
        item,
        index
      }
    }))
  }
}

// 处理数据绑定事件
const handleDataBinding = (event) => {
  const { componentId, property, data } = event.detail
  
  if (componentId === props.component.id) {
    console.log(`DataDisplayComponent: Received data binding for ${componentId}:`, data)
    componentData.value = data
    errorMessage.value = ''
  }
}

// 处理API数据更新事件
const handleApiDataUpdate = (event) => {
  const { apiKey, data } = event.detail
  
  // 检查组件是否引用了此API数据
  const dataSource = props.component.props?.dataSource || ''
  if (dataSource.includes(`{{apiData.${apiKey}}}`)) {
    console.log(`DataDisplayComponent: API data updated for ${apiKey}, refreshing display`)
    componentData.value = null // 触发重新计算
    errorMessage.value = ''
  }
}

// 处理变量更新事件
const handleVariableUpdate = (event) => {
  const { name, value } = event.detail
  
  // 检查组件是否引用了此变量
  const dataSource = props.component.props?.dataSource || ''
  if (dataSource.includes(`{{variables.${name}}}`) || dataSource.includes(`{{${name}}}`)) {
    console.log(`DataDisplayComponent: Variable ${name} updated, refreshing display`)
    componentData.value = null // 触发重新计算
    errorMessage.value = ''
  }
}

// 处理加载状态更新
const handleLoadingUpdate = (event) => {
  const { apiKey, loading } = event.detail
  
  // 检查是否与当前组件相关
  const dataSource = props.component.props?.dataSource || ''
  if (dataSource.includes(`{{apiData.${apiKey}}}`)) {
    isLoading.value = loading
  }
}

// 处理错误状态更新
const handleErrorUpdate = (event) => {
  const { apiKey, error } = event.detail
  
  // 检查是否与当前组件相关
  const dataSource = props.component.props?.dataSource || ''
  if (dataSource.includes(`{{apiData.${apiKey}}}`)) {
    errorMessage.value = error
    isLoading.value = false
  }
}

// 组件挂载时添加事件监听
onMounted(() => {
  document.addEventListener('componentDataBinding', handleDataBinding)
  document.addEventListener('apiDataUpdate', handleApiDataUpdate)
  document.addEventListener('variableUpdate', handleVariableUpdate)
  document.addEventListener('loadingUpdate', handleLoadingUpdate)
  document.addEventListener('errorUpdate', handleErrorUpdate)
  
  console.log(`DataDisplayComponent mounted: ${props.component.id}`)
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('componentDataBinding', handleDataBinding)
  document.removeEventListener('apiDataUpdate', handleApiDataUpdate)
  document.removeEventListener('variableUpdate', handleVariableUpdate)
  document.removeEventListener('loadingUpdate', handleLoadingUpdate)
  document.removeEventListener('errorUpdate', handleErrorUpdate)
  
  console.log(`DataDisplayComponent unmounted: ${props.component.id}`)
})
</script>

<style scoped>
.data-display-component {
  width: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}

.loading-state, .error-state, .empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;
  flex-direction: column;
  gap: 8px;
}

.error-state {
  color: #f56c6c;
}

.list-header, .object-header, .simple-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.list-header h4, .object-header h4, .simple-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.item-count {
  font-size: 12px;
  color: #909399;
}

.list-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.list-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  transition: all 0.3s;
}

.list-item.clickable {
  cursor: pointer;
}

.list-item.clickable:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.item-card .item-field {
  display: flex;
  margin-bottom: 8px;
}

.item-card .item-field:last-child {
  margin-bottom: 0;
}

.item-row {
  display: flex;
  gap: 16px;
}

.item-cell {
  flex: 1;
  padding: 4px 8px;
  border-right: 1px solid #e4e7ed;
}

.item-cell:last-child {
  border-right: none;
}

.object-fields {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.object-field, .item-field {
  display: flex;
  align-items: center;
}

.field-label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.field-value {
  color: #303133;
  word-break: break-all;
}

.simple-value {
  font-size: 16px;
  color: #303133;
  text-align: center;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
