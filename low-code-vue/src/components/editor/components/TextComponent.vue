<template>
  <div class="text-component" :style="componentStyles">
    <div v-if="isEditing" class="text-editor">
      <el-input
        type="textarea"
        v-model="editingContent"
        :rows="getRows(editingContent)"
        @blur="saveContent"
        ref="textareaRef"
        autofocus
      />
    </div>
    <div v-else @dblclick="startEditing" class="text-content">
      {{ displayContent }}
    </div>
  </div>
</template>

<script setup>
import { computed, ref, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { dataManager } from '../../../utils/dataManager'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Editing state
const isEditing = ref(false)
const editingContent = ref('')
const textareaRef = ref(null)

// 组件数据
const componentData = ref(null)

// 计算显示内容
const displayContent = computed(() => {
  // 如果有绑定的数据，优先显示绑定数据
  if (componentData.value !== null && componentData.value !== undefined) {
    if (typeof componentData.value === 'object') {
      return JSON.stringify(componentData.value, null, 2)
    }
    return String(componentData.value)
  }

  // 解析数据绑定表达式
  const content = props.component.props?.content || '文本内容'
  const parsedContent = dataManager.parseDataBinding(content)

  if (parsedContent !== content) {
    // 如果解析出了数据绑定，格式化显示
    if (typeof parsedContent === 'object') {
      return JSON.stringify(parsedContent, null, 2)
    }
    return String(parsedContent)
  }

  return content
})

// Computed styles
const componentStyles = computed(() => {
  return props.component.styles || {}
})

// Start editing
const startEditing = () => {
  editingContent.value = props.component.props.content
  isEditing.value = true

  // Focus the textarea after it's rendered
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.focus()
    }
  })
}

// Save content
const saveContent = () => {
  isEditing.value = false

  // Only update if content has changed
  if (editingContent.value !== props.component.props.content) {
    const updatedComponent = {
      ...props.component,
      props: {
        ...props.component.props,
        content: editingContent.value
      }
    }

    emit('update', updatedComponent)
  }
}

// Calculate rows for textarea based on content
const getRows = (content) => {
  if (!content) return 1
  const lineCount = (content.match(/\n/g) || []).length + 1
  return Math.min(Math.max(lineCount, 1), 5) // Min 1, max 5 rows
}

// 处理数据绑定事件
const handleDataBinding = (event) => {
  const { componentId, property, data } = event.detail

  if (componentId === props.component.id) {
    console.log(`TextComponent: Received data binding for ${componentId}:`, data)
    componentData.value = data
  }
}

// 处理API数据更新事件
const handleApiDataUpdate = (event) => {
  const { apiKey, data } = event.detail

  // 检查组件内容是否包含对此API数据的引用
  const content = props.component.props?.content || ''
  if (content.includes(`{{apiData.${apiKey}}}`)) {
    console.log(`TextComponent: API data updated for ${apiKey}, refreshing display`)
    // 触发重新计算
    componentData.value = null
  }
}

// 处理变量更新事件
const handleVariableUpdate = (event) => {
  const { name, value } = event.detail

  // 检查组件内容是否包含对此变量的引用
  const content = props.component.props?.content || ''
  if (content.includes(`{{variables.${name}}}`) || content.includes(`{{${name}}}`)) {
    console.log(`TextComponent: Variable ${name} updated, refreshing display`)
    // 触发重新计算
    componentData.value = null
  }
}

// 组件挂载时添加事件监听
onMounted(() => {
  document.addEventListener('componentDataBinding', handleDataBinding)
  document.addEventListener('apiDataUpdate', handleApiDataUpdate)
  document.addEventListener('variableUpdate', handleVariableUpdate)

  console.log(`TextComponent mounted: ${props.component.id}`)
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('componentDataBinding', handleDataBinding)
  document.removeEventListener('apiDataUpdate', handleApiDataUpdate)
  document.removeEventListener('variableUpdate', handleVariableUpdate)

  console.log(`TextComponent unmounted: ${props.component.id}`)
})
</script>

<style scoped>
.text-component {
  width: 100%;
}

.text-content {
  cursor: text;
  white-space: pre-wrap;
}

.text-editor {
  width: 100%;
}
</style>
