# 存储配置切换测试指南

## 🧪 测试场景

### 1. 基础配置测试

#### 测试当前配置
```bash
# 获取当前存储配置
curl -X GET "http://localhost:8080/api/storage/config" | jq

# 预期响应
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "currentType": "minio",
    "fallbackToLocal": false,
    "localBasePath": "./uploads",
    "localUrlPrefix": "http://localhost:8080/api/files",
    "actualStorageType": "minio",
    "minioAvailable": true,
    "lastUpdated": "2024-01-01T12:00:00"
  }
}
```

#### 测试存储连接
```bash
# 测试所有存储连接状态
curl -X GET "http://localhost:8080/api/storage/config/test" | jq

# 预期响应
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "testTime": "2024-01-01T12:00:00",
    "configuredType": "minio",
    "currentStorageType": "minio",
    "fallbackToLocal": false,
    "minioStatus": "AVAILABLE",
    "localStatus": "AVAILABLE",
    "minioAvailable": true,
    "localAvailable": true,
    "localBasePath": "./uploads"
  }
}
```

### 2. 存储类型切换测试

#### 切换到本地存储
```bash
# 方式1：使用查询参数（快速切换）
curl -X POST "http://localhost:8080/api/storage/config/switch-quick?targetType=local&enableFallback=false"

# 方式2：使用请求体（完整切换）
curl -X POST "http://localhost:8080/api/storage/config/switch" \
  -H "Content-Type: application/json" \
  -d '{
    "targetType": "local",
    "enableFallback": false,
    "reason": "测试本地存储功能"
  }'

# 预期响应
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "currentType": "local",
    "actualStorageType": "local",
    "fallbackToLocal": false,
    "switchMessage": "存储模式切换成功，当前配置: local，实际使用: local，切换原因: 测试本地存储功能"
  }
}
```

#### 切换到MinIO存储
```bash
curl -X POST "http://localhost:8080/api/storage/config/switch-quick?targetType=minio&enableFallback=false"

# 预期响应
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "currentType": "minio",
    "actualStorageType": "minio",
    "fallbackToLocal": false,
    "switchMessage": "存储模式切换成功，当前配置: minio，实际使用: minio"
  }
}
```

### 3. 文件上传测试

#### 测试MinIO存储上传
```bash
# 确保配置为MinIO
curl -X POST "http://localhost:8080/api/storage/config/switch-quick?targetType=minio&enableFallback=false"

# 上传测试图片
curl -X POST "http://localhost:8080/api/images/upload" \
  -F "file=@test-image.jpg" \
  -F "category=2" \
  -F "remark=MinIO存储测试"

# 检查日志应显示：使用MinIO存储上传文件
```

#### 测试本地存储上传
```bash
# 切换到本地存储
curl -X POST "http://localhost:8080/api/storage/config/switch-quick?targetType=local&enableFallback=false"

# 上传测试图片
curl -X POST "http://localhost:8080/api/images/upload" \
  -F "file=@test-image.jpg" \
  -F "category=2" \
  -F "remark=本地存储测试"

# 检查日志应显示：使用本地存储上传文件
```

### 4. 错误处理测试

#### 测试MinIO不可用时的行为
```bash
# 停止MinIO服务（模拟不可用）
docker stop minio

# 配置为强制使用MinIO
curl -X POST "http://localhost:8080/api/storage/config/switch-quick?targetType=minio&enableFallback=false"

# 尝试上传文件
curl -X POST "http://localhost:8080/api/images/upload" \
  -F "file=@test-image.jpg" \
  -F "category=2"

# 预期响应：错误信息，提示MinIO不可用
{
  "code": 500,
  "message": "文件上传失败，当前存储类型: minio，错误: xxx。请检查MinIO服务是否正常运行，或切换到本地存储模式。"
}
```

#### 测试配置验证
```bash
# 测试无效的存储类型
curl -X POST "http://localhost:8080/api/storage/config/type?storageType=invalid"

# 预期响应
{
  "code": 500,
  "message": "参数错误: 不支持的存储类型: invalid，支持的类型: auto, minio, local"
}
```

### 5. 自动模式测试（兼容性）

#### 启用自动模式
```bash
# 切换到自动模式并启用回退
curl -X POST "http://localhost:8080/api/storage/config/switch" \
  -H "Content-Type: application/json" \
  -d '{
    "targetType": "auto",
    "enableFallback": true,
    "reason": "测试自动模式兼容性"
  }'
```

#### 测试自动回退
```bash
# 确保MinIO不可用
docker stop minio

# 上传文件，应该自动回退到本地存储
curl -X POST "http://localhost:8080/api/images/upload" \
  -F "file=@test-image.jpg" \
  -F "category=2" \
  -F "remark=自动回退测试"

# 检查日志应显示：MinIO上传失败，回退到本地存储
```

## 📊 测试检查清单

### ✅ 功能测试
- [ ] 配置查询接口正常工作
- [ ] 存储连接测试接口正常工作
- [ ] 存储类型切换接口正常工作
- [ ] MinIO存储模式文件上传正常
- [ ] 本地存储模式文件上传正常
- [ ] 文件下载在不同存储模式下正常工作
- [ ] 文件删除在不同存储模式下正常工作

### ✅ 错误处理测试
- [ ] MinIO不可用时错误提示正确
- [ ] 无效配置参数时错误提示正确
- [ ] 本地存储权限问题时错误提示正确
- [ ] 不支持的存储类型时错误提示正确

### ✅ 兼容性测试
- [ ] 自动模式仍然正常工作
- [ ] 回退机制在auto模式下正常工作
- [ ] 明确配置模式下不会意外回退
- [ ] 配置变更立即生效

### ✅ 性能测试
- [ ] 配置切换响应时间合理
- [ ] 文件操作性能无明显下降
- [ ] 存储状态检查不影响正常操作

## 🔧 测试环境准备

### 1. 启动MinIO服务
```bash
docker run -d \
  --name minio \
  -p 9000:9000 \
  -p 9001:9001 \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin \
  minio/minio server /data --console-address ":9001"
```

### 2. 准备测试文件
```bash
# 创建测试图片
echo "test image content" > test-image.jpg
```

### 3. 检查应用配置
```bash
# 确认配置文件设置正确
grep "file.storage" src/main/resources/application.properties
```

## 📝 测试报告模板

```markdown
## 存储配置切换测试报告

### 测试环境
- 应用版本：v1.0.0
- MinIO版本：latest
- 测试时间：2024-01-01

### 测试结果
| 测试项目 | 状态 | 备注 |
|---------|------|------|
| 配置查询 | ✅ | 正常 |
| 存储切换 | ✅ | 正常 |
| MinIO上传 | ✅ | 正常 |
| 本地上传 | ✅ | 正常 |
| 错误处理 | ✅ | 正常 |

### 发现问题
无

### 建议
配置功能工作正常，建议在生产环境中使用明确的存储类型配置。
```

通过这些测试，您可以验证新的存储配置选择功能是否正常工作，确保系统按照配置的存储类型严格执行，而不会意外地自动切换存储方式。
