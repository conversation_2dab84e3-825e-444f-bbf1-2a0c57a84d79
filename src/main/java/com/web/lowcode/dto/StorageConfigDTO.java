package com.web.lowcode.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 存储配置DTO
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@Schema(description = "存储配置数据传输对象")
public class StorageConfigDTO {
    
    /**
     * 存储类型配置请求
     */
    @Data
    @Schema(description = "存储类型配置请求")
    public static class StorageTypeRequest {
        
        @NotBlank(message = "存储类型不能为空")
        @Pattern(regexp = "^(auto|minio|local)$", message = "存储类型只能是: auto, minio, local")
        @Schema(description = "存储类型", allowableValues = {"auto", "minio", "local"}, example = "auto")
        private String storageType;
        
        @Schema(description = "是否启用回退到本地存储", example = "true")
        private Boolean fallbackToLocal;
    }
    
    /**
     * 存储配置响应
     */
    @Data
    @Schema(description = "存储配置响应")
    public static class StorageConfigResponse {
        
        @Schema(description = "配置的存储类型", example = "auto")
        private String currentType;
        
        @Schema(description = "实际使用的存储类型", example = "minio")
        private String actualStorageType;
        
        @Schema(description = "是否启用回退到本地存储", example = "true")
        private Boolean fallbackToLocal;
        
        @Schema(description = "本地存储基础路径", example = "./uploads")
        private String localBasePath;
        
        @Schema(description = "本地存储URL前缀", example = "http://localhost:8080/api/files")
        private String localUrlPrefix;
        
        @Schema(description = "MinIO是否可用", example = "true")
        private Boolean minioAvailable;
        
        @Schema(description = "最后更新时间", example = "2024-01-01T12:00:00")
        private String lastUpdated;
        
        @Schema(description = "存储类型描述", example = "自动模式：当前使用MinIO")
        private String description;
    }
    
    /**
     * 存储连接测试响应
     */
    @Data
    @Schema(description = "存储连接测试响应")
    public static class StorageTestResponse {
        
        @Schema(description = "测试时间", example = "2024-01-01T12:00:00")
        private String testTime;
        
        @Schema(description = "配置的存储类型", example = "auto")
        private String configuredType;
        
        @Schema(description = "当前使用的存储类型", example = "minio")
        private String currentStorageType;
        
        @Schema(description = "是否启用回退", example = "true")
        private Boolean fallbackToLocal;
        
        @Schema(description = "MinIO状态", example = "AVAILABLE")
        private String minioStatus;
        
        @Schema(description = "本地存储状态", example = "AVAILABLE")
        private String localStatus;
        
        @Schema(description = "MinIO是否可用", example = "true")
        private Boolean minioAvailable;
        
        @Schema(description = "本地存储是否可用", example = "true")
        private Boolean localAvailable;
        
        @Schema(description = "本地存储基础路径", example = "./uploads")
        private String localBasePath;
        
        @Schema(description = "MinIO错误信息（如果有）")
        private String minioError;
        
        @Schema(description = "本地存储错误信息（如果有）")
        private String localError;
    }
    
    /**
     * 存储模式切换请求
     */
    @Data
    @Schema(description = "存储模式切换请求")
    public static class StorageSwitchRequest {
        
        @NotBlank(message = "目标存储类型不能为空")
        @Pattern(regexp = "^(auto|minio|local)$", message = "存储类型只能是: auto, minio, local")
        @Schema(description = "目标存储类型", allowableValues = {"auto", "minio", "local"}, example = "minio")
        private String targetType;
        
        @Schema(description = "是否启用回退到本地存储", example = "true")
        private Boolean enableFallback = true;
        
        @Schema(description = "切换原因或备注")
        private String reason;
    }
}
