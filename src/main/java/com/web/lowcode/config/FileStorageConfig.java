package com.web.lowcode.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 文件存储配置类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Configuration
public class FileStorageConfig {

    /**
     * 文件存储配置属性
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "file.storage")
    public static class FileStorageProperties {

        /**
         * 存储类型：
         * - minio: 强制使用MinIO存储，不可用时直接报错
         * - local: 强制使用本地存储
         * - auto: 自动选择（不推荐，建议明确指定存储类型）
         */
        private String type = "minio";

        /**
         * 是否在MinIO不可用时回退到本地存储
         * 注意：仅在type=auto时有效，明确指定存储类型时不会回退
         */
        private boolean fallbackToLocal = false;

        /**
         * 本地存储配置
         */
        private Local local = new Local();

        /**
         * 验证存储类型配置
         */
        public boolean isValidStorageType() {
            if (type == null) return false;
            String normalizedType = type.toLowerCase().trim();
            return "minio".equals(normalizedType) ||
                   "local".equals(normalizedType) ||
                   "auto".equals(normalizedType);
        }

        /**
         * 获取标准化的存储类型
         */
        public String getNormalizedType() {
            return type == null ? "minio" : type.toLowerCase().trim();
        }
        
        @Data
        public static class Local {
            /**
             * 本地存储基础路径
             */
            private String basePath = "./uploads";
            
            /**
             * 访问URL前缀
             */
            private String urlPrefix = "http://localhost:8080/api/files";
        }
    }
    
    /**
     * 存储类型枚举
     */
    public enum StorageType {
        /**
         * 自动选择（优先MinIO，失败时使用本地）
         */
        AUTO,
        
        /**
         * MinIO存储
         */
        MINIO,
        
        /**
         * 本地存储
         */
        LOCAL
    }
}
