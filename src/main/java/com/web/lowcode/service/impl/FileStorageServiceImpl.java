package com.web.lowcode.service.impl;

import com.web.lowcode.config.FileStorageConfig;
import com.web.lowcode.service.FileStorageService;
import com.web.lowcode.service.LocalFileService;
import com.web.lowcode.service.MinioService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 统一文件存储服务实现类
 * 支持MinIO和本地存储的自动切换
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileStorageServiceImpl implements FileStorageService {
    
    private final MinioService minioService;
    private final LocalFileService localFileService;
    private final FileStorageConfig.FileStorageProperties fileStorageProperties;
    
    @Override
    public String uploadFile(MultipartFile file, String relativePath) {
        String storageType = determineStorageType();
        
        try {
            if ("minio".equals(storageType)) {
                log.info("使用MinIO存储上传文件: {}", relativePath);
                return minioService.uploadFile(file, relativePath);
            } else {
                log.info("使用本地存储上传文件: {}", relativePath);
                return localFileService.uploadFile(file, relativePath);
            }
        } catch (Exception e) {
            log.error("文件上传失败，存储类型: {}, 路径: {}", storageType, relativePath, e);
            
            // 如果是MinIO失败且允许回退到本地存储
            if ("minio".equals(storageType) && fileStorageProperties.isFallbackToLocal()) {
                log.warn("MinIO上传失败，回退到本地存储: {}", relativePath);
                try {
                    return localFileService.uploadFile(file, relativePath);
                } catch (Exception localException) {
                    log.error("本地存储回退也失败: {}", relativePath, localException);
                    throw new RuntimeException("文件上传失败，MinIO和本地存储都不可用: " + localException.getMessage());
                }
            }
            
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public String uploadFile(InputStream inputStream, String relativePath, String contentType, long size) {
        String storageType = determineStorageType();
        
        try {
            if ("minio".equals(storageType)) {
                log.info("使用MinIO存储上传文件流: {}", relativePath);
                return minioService.uploadFile(inputStream, relativePath, contentType, size);
            } else {
                log.info("使用本地存储上传文件流: {}", relativePath);
                return localFileService.uploadFile(inputStream, relativePath, contentType, size);
            }
        } catch (Exception e) {
            log.error("文件流上传失败，存储类型: {}, 路径: {}", storageType, relativePath, e);
            
            // 如果是MinIO失败且允许回退到本地存储
            if ("minio".equals(storageType) && fileStorageProperties.isFallbackToLocal()) {
                log.warn("MinIO文件流上传失败，回退到本地存储: {}", relativePath);
                try {
                    return localFileService.uploadFile(inputStream, relativePath, contentType, size);
                } catch (Exception localException) {
                    log.error("本地存储文件流回退也失败: {}", relativePath, localException);
                    throw new RuntimeException("文件流上传失败，MinIO和本地存储都不可用: " + localException.getMessage());
                }
            }
            
            throw new RuntimeException("文件流上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public InputStream downloadFile(String relativePath) {
        // 首先尝试从MinIO下载
        if (isMinioAvailable()) {
            try {
                return minioService.downloadFile(relativePath);
            } catch (Exception e) {
                log.warn("从MinIO下载文件失败，尝试本地存储: {}", relativePath);
            }
        }
        
        // 从本地存储下载
        return localFileService.downloadFile(relativePath);
    }
    
    @Override
    public void deleteFile(String relativePath) {
        boolean minioDeleted = false;
        boolean localDeleted = false;
        
        // 尝试从MinIO删除
        if (isMinioAvailable()) {
            try {
                minioService.deleteFile(relativePath);
                minioDeleted = true;
                log.info("从MinIO删除文件成功: {}", relativePath);
            } catch (Exception e) {
                log.warn("从MinIO删除文件失败: {}", relativePath, e);
            }
        }
        
        // 尝试从本地存储删除
        try {
            localFileService.deleteFile(relativePath);
            localDeleted = true;
            log.info("从本地存储删除文件成功: {}", relativePath);
        } catch (Exception e) {
            log.warn("从本地存储删除文件失败: {}", relativePath, e);
        }
        
        if (!minioDeleted && !localDeleted) {
            throw new RuntimeException("文件删除失败，MinIO和本地存储都无法删除: " + relativePath);
        }
    }
    
    @Override
    public void deleteFiles(String[] relativePaths) {
        for (String relativePath : relativePaths) {
            try {
                deleteFile(relativePath);
            } catch (Exception e) {
                log.error("批量删除文件失败: {}", relativePath, e);
            }
        }
    }
    
    @Override
    public String getFileUrl(String relativePath) {
        // 首先检查文件在哪里存储，然后生成对应的URL

        // 检查MinIO中是否存在文件
        if (isMinioAvailable()) {
            try {
                if (minioService.fileExists(relativePath)) {
                    log.debug("文件在MinIO中存在，生成MinIO URL: {}", relativePath);
                    return minioService.getFileUrl(relativePath);
                }
            } catch (Exception e) {
                log.debug("检查MinIO文件存在性失败: {}", relativePath);
            }
        }

        // 检查本地存储中是否存在文件
        if (localFileService.fileExists(relativePath)) {
            log.debug("文件在本地存储中存在，生成本地URL: {}", relativePath);
            return localFileService.getFileUrl(relativePath);
        }

        // 如果文件都不存在，根据当前存储策略生成URL
        String storageType = determineStorageType();
        log.warn("文件在任何存储中都不存在，根据当前策略生成URL: {}, 存储类型: {}", relativePath, storageType);

        if ("minio".equals(storageType)) {
            return minioService.getFileUrl(relativePath);
        } else {
            return localFileService.getFileUrl(relativePath);
        }
    }
    
    @Override
    public Map<String, Object> getFileInfo(String relativePath) {
        // 首先尝试从MinIO获取
        if (isMinioAvailable()) {
            try {
                Map<String, Object> info = minioService.getFileInfo(relativePath);
                info.put("storageType", "minio");
                return info;
            } catch (Exception e) {
                log.warn("从MinIO获取文件信息失败，尝试本地存储: {}", relativePath);
            }
        }
        
        // 从本地存储获取
        Map<String, Object> info = localFileService.getFileInfo(relativePath);
        info.put("storageType", "local");
        return info;
    }
    
    @Override
    public boolean fileExists(String relativePath) {
        // 检查MinIO
        if (isMinioAvailable()) {
            try {
                if (minioService.fileExists(relativePath)) {
                    return true;
                }
            } catch (Exception e) {
                log.warn("检查MinIO文件存在性失败: {}", relativePath);
            }
        }
        
        // 检查本地存储
        return localFileService.fileExists(relativePath);
    }
    
    @Override
    public void copyFile(String sourceRelativePath, String targetRelativePath) {
        String storageType = determineStorageType();
        
        try {
            if ("minio".equals(storageType)) {
                minioService.copyFile(sourceRelativePath, targetRelativePath);
            } else {
                localFileService.copyFile(sourceRelativePath, targetRelativePath);
            }
        } catch (Exception e) {
            log.error("文件复制失败，存储类型: {}", storageType, e);
            
            // 如果是MinIO失败且允许回退到本地存储
            if ("minio".equals(storageType) && fileStorageProperties.isFallbackToLocal()) {
                log.warn("MinIO文件复制失败，回退到本地存储");
                localFileService.copyFile(sourceRelativePath, targetRelativePath);
            } else {
                throw new RuntimeException("文件复制失败: " + e.getMessage());
            }
        }
    }
    
    @Override
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();
        result.put("testTime", LocalDateTime.now());
        result.put("configuredType", fileStorageProperties.getType());
        result.put("fallbackToLocal", fileStorageProperties.isFallbackToLocal());
        
        // 测试MinIO连接
        boolean minioAvailable = false;
        try {
            Map<String, Object> minioTest = minioService.bucketExists("test") ? new HashMap<>() : new HashMap<>();
            minioAvailable = true;
            result.put("minioStatus", "AVAILABLE");
            result.put("minioTest", minioTest);
        } catch (Exception e) {
            result.put("minioStatus", "UNAVAILABLE");
            result.put("minioError", e.getMessage());
        }
        
        // 测试本地存储
        boolean localAvailable = false;
        try {
            localFileService.initStorageDirectories();
            localAvailable = true;
            result.put("localStatus", "AVAILABLE");
            result.put("localBasePath", fileStorageProperties.getLocal().getBasePath());
        } catch (Exception e) {
            result.put("localStatus", "UNAVAILABLE");
            result.put("localError", e.getMessage());
        }
        
        // 确定当前使用的存储类型
        String currentType = determineStorageType();
        result.put("currentStorageType", currentType);
        result.put("minioAvailable", minioAvailable);
        result.put("localAvailable", localAvailable);
        
        return result;
    }
    
    @Override
    public String getCurrentStorageType() {
        return determineStorageType();
    }
    
    @Override
    public boolean isMinioAvailable() {
        try {
            return minioService.bucketExists("test") || true; // 简单的连接测试
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 确定使用的存储类型
     */
    private String determineStorageType() {
        String configuredType = fileStorageProperties.getType();
        
        switch (configuredType.toLowerCase()) {
            case "minio":
                return "minio";
            case "local":
                return "local";
            case "auto":
            default:
                // 自动选择：优先MinIO，不可用时使用本地存储
                if (isMinioAvailable()) {
                    return "minio";
                } else {
                    log.info("MinIO不可用，使用本地存储");
                    return "local";
                }
        }
    }
}
