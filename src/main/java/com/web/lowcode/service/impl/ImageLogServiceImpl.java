package com.web.lowcode.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.web.lowcode.entity.ImageLogEntity;
import com.web.lowcode.mapper.ImageLogMapper;
import com.web.lowcode.service.ImageLogService;
import com.web.lowcode.security.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图片操作日志服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageLogServiceImpl extends ServiceImpl<ImageLogMapper, ImageLogEntity> implements ImageLogService {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public void recordLog(Long imageId, String operationType, String operationDesc, 
                         Object beforeData, Object afterData, String result, String errorMessage) {
        try {
            ImageLogEntity logEntity = new ImageLogEntity();
            logEntity.setImageId(imageId);
            logEntity.setOperationType(operationType);
            logEntity.setOperationDesc(operationDesc);
            logEntity.setBeforeData(objectToJson(beforeData));
            logEntity.setAfterData(objectToJson(afterData));
            logEntity.setOperatorId(getCurrentUserId());
            logEntity.setOperatorName(getCurrentUserName());
            logEntity.setIpAddress(getClientIpAddress());
            logEntity.setUserAgent(getUserAgent());
            logEntity.setResult(result);
            logEntity.setErrorMessage(errorMessage);
            logEntity.setCreateTime(LocalDateTime.now());
            
            save(logEntity);
            
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
            // 日志记录失败不应该影响主业务流程
        }
    }
    
    @Override
    public IPage<ImageLogEntity> getLogPage(int current, int size, Long imageId, String operationType, 
                                           Long operatorId, String result, LocalDateTime startTime, LocalDateTime endTime) {
        Page<ImageLogEntity> page = new Page<>(current, size);
        return baseMapper.selectLogPage(page, imageId, operationType, operatorId, result, startTime, endTime);
    }
    
    @Override
    public List<ImageLogEntity> getLogsByImageId(Long imageId) {
        return baseMapper.selectByImageId(imageId);
    }
    
    @Override
    public Long countOperations(String operationType, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countByOperationType(operationType, startTime, endTime);
    }
    
    @Override
    public Map<String, Object> getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 各操作类型统计
        String[] operationTypes = {"CREATE", "UPDATE", "DELETE", "VIEW", "DOWNLOAD"};
        for (String operationType : operationTypes) {
            Long count = countOperations(operationType, startTime, endTime);
            statistics.put(operationType.toLowerCase() + "Count", count != null ? count : 0);
        }
        
        // 总操作次数
        LambdaQueryWrapper<ImageLogEntity> wrapper = new LambdaQueryWrapper<>();
        if (startTime != null) {
            wrapper.ge(ImageLogEntity::getCreateTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(ImageLogEntity::getCreateTime, endTime);
        }
        long totalCount = count(wrapper);
        statistics.put("totalCount", totalCount);
        
        // 成功失败统计
        wrapper.clear();
        wrapper.eq(ImageLogEntity::getResult, "SUCCESS");
        if (startTime != null) {
            wrapper.ge(ImageLogEntity::getCreateTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(ImageLogEntity::getCreateTime, endTime);
        }
        long successCount = count(wrapper);
        statistics.put("successCount", successCount);
        statistics.put("failedCount", totalCount - successCount);
        
        return statistics;
    }
    
    @Override
    public int cleanExpiredLogs(int days) {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
            int deletedCount = baseMapper.deleteExpiredLogs(expireTime);
            log.info("清理过期日志完成，共清理 {} 条日志", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
            throw new RuntimeException("清理过期日志失败: " + e.getMessage());
        }
    }
    
    /**
     * 对象转JSON字符串
     */
    private String objectToJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.warn("对象转JSON失败", e);
            return obj.toString();
        }
    }
    
    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        try {
            return SecurityUtils.getCurrentUserId();
        } catch (Exception e) {
            return 1L; // 默认系统用户
        }
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUserName() {
        try {
            return SecurityUtils.getCurrentUserName();
        } catch (Exception e) {
            return "system"; // 默认系统用户
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String xForwardedFor = request.getHeader("X-Forwarded-For");
                if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
                    return xForwardedFor.split(",")[0].trim();
                }
                
                String xRealIp = request.getHeader("X-Real-IP");
                if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
                    return xRealIp;
                }
                
                return request.getRemoteAddr();
            }
        } catch (Exception e) {
            log.warn("获取客户端IP地址失败", e);
        }
        return "unknown";
    }
    
    /**
     * 获取用户代理信息
     */
    private String getUserAgent() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return request.getHeader("User-Agent");
            }
        } catch (Exception e) {
            log.warn("获取用户代理信息失败", e);
        }
        return "unknown";
    }
}
