package com.web.lowcode.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Map;

/**
 * 统一文件存储服务接口
 * 支持MinIO和本地存储的自动切换
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface FileStorageService {
    
    /**
     * 上传文件
     * @param file 文件
     * @param relativePath 相对路径
     * @return 文件访问URL
     */
    String uploadFile(MultipartFile file, String relativePath);
    
    /**
     * 上传文件流
     * @param inputStream 输入流
     * @param relativePath 相对路径
     * @param contentType 内容类型
     * @param size 文件大小
     * @return 文件访问URL
     */
    String uploadFile(InputStream inputStream, String relativePath, String contentType, long size);
    
    /**
     * 下载文件
     * @param relativePath 相对路径
     * @return 文件输入流
     */
    InputStream downloadFile(String relativePath);
    
    /**
     * 删除文件
     * @param relativePath 相对路径
     */
    void deleteFile(String relativePath);
    
    /**
     * 批量删除文件
     * @param relativePaths 相对路径数组
     */
    void deleteFiles(String[] relativePaths);
    
    /**
     * 获取文件访问URL
     * @param relativePath 相对路径
     * @return 文件访问URL
     */
    String getFileUrl(String relativePath);
    
    /**
     * 获取文件信息
     * @param relativePath 相对路径
     * @return 文件信息
     */
    Map<String, Object> getFileInfo(String relativePath);
    
    /**
     * 检查文件是否存在
     * @param relativePath 相对路径
     * @return 是否存在
     */
    boolean fileExists(String relativePath);
    
    /**
     * 复制文件
     * @param sourceRelativePath 源文件相对路径
     * @param targetRelativePath 目标文件相对路径
     */
    void copyFile(String sourceRelativePath, String targetRelativePath);
    
    /**
     * 测试存储服务连接
     * @return 测试结果
     */
    Map<String, Object> testConnection();
    
    /**
     * 获取当前使用的存储类型
     * @return 存储类型
     */
    String getCurrentStorageType();
    
    /**
     * 检查MinIO是否可用
     * @return 是否可用
     */
    boolean isMinioAvailable();

    /**
     * 动态设置存储类型
     * @param storageType 存储类型：auto/minio/local
     */
    void setStorageType(String storageType);

    /**
     * 动态设置是否允许回退到本地存储
     * @param fallbackToLocal 是否允许回退
     */
    void setFallbackToLocal(boolean fallbackToLocal);

    /**
     * 获取存储配置信息
     * @return 配置信息
     */
    Map<String, Object> getStorageConfig();
}
