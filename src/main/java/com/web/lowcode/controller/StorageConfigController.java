package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.service.FileStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 存储配置管理控制器
 * 提供动态配置文件存储类型的功能
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/storage/config")
@RequiredArgsConstructor
@Tag(name = "存储配置管理", description = "文件存储配置管理相关接口")
public class StorageConfigController {
    
    private final FileStorageService fileStorageService;
    
    /**
     * 获取当前存储配置
     */
    @GetMapping
    @Operation(summary = "获取当前存储配置")
    public Result<Map<String, Object>> getStorageConfig() {
        try {
            Map<String, Object> config = fileStorageService.getStorageConfig();
            log.info("获取存储配置成功: {}", config);
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取存储配置失败", e);
            return Result.error("获取存储配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置存储类型
     */
    @PostMapping("/type")
    @Operation(summary = "设置存储类型")
    public Result<String> setStorageType(
            @Parameter(description = "存储类型：auto(自动选择)/minio(MinIO)/local(本地存储)") 
            @RequestParam String storageType) {
        try {
            log.info("请求设置存储类型: {}", storageType);
            fileStorageService.setStorageType(storageType);
            
            String actualType = fileStorageService.getCurrentStorageType();
            String message = String.format("存储类型设置成功，配置类型: %s，实际使用: %s", storageType, actualType);
            
            log.info(message);
            return Result.success(message);
        } catch (IllegalArgumentException e) {
            log.warn("设置存储类型参数错误: {}", e.getMessage());
            return Result.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("设置存储类型失败", e);
            return Result.error("设置存储类型失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置是否允许回退到本地存储
     */
    @PostMapping("/fallback")
    @Operation(summary = "设置是否允许回退到本地存储")
    public Result<String> setFallbackToLocal(
            @Parameter(description = "是否允许回退到本地存储") 
            @RequestParam boolean fallbackToLocal) {
        try {
            log.info("请求设置回退到本地存储: {}", fallbackToLocal);
            fileStorageService.setFallbackToLocal(fallbackToLocal);
            
            String message = "回退设置更新成功: " + (fallbackToLocal ? "启用" : "禁用") + "回退到本地存储";
            log.info(message);
            return Result.success(message);
        } catch (Exception e) {
            log.error("设置回退选项失败", e);
            return Result.error("设置回退选项失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试存储连接
     */
    @GetMapping("/test")
    @Operation(summary = "测试存储连接")
    public Result<Map<String, Object>> testStorageConnection() {
        try {
            log.info("开始测试存储连接");
            Map<String, Object> testResult = fileStorageService.testConnection();
            log.info("存储连接测试完成: {}", testResult);
            return Result.success(testResult);
        } catch (Exception e) {
            log.error("测试存储连接失败", e);
            return Result.error("测试存储连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前实际使用的存储类型
     */
    @GetMapping("/current-type")
    @Operation(summary = "获取当前实际使用的存储类型")
    public Result<Map<String, Object>> getCurrentStorageType() {
        try {
            String currentType = fileStorageService.getCurrentStorageType();
            boolean minioAvailable = fileStorageService.isMinioAvailable();
            
            Map<String, Object> result = Map.of(
                "currentType", currentType,
                "minioAvailable", minioAvailable,
                "description", getStorageTypeDescription(currentType, minioAvailable)
            );
            
            log.info("获取当前存储类型: {}", result);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取当前存储类型失败", e);
            return Result.error("获取当前存储类型失败: " + e.getMessage());
        }
    }
    
    /**
     * 一键切换存储模式
     */
    @PostMapping("/switch")
    @Operation(summary = "一键切换存储模式")
    public Result<Map<String, Object>> switchStorageMode(
            @Parameter(description = "目标存储类型") @RequestParam String targetType,
            @Parameter(description = "是否启用回退", required = false) @RequestParam(defaultValue = "true") boolean enableFallback) {
        try {
            log.info("请求一键切换存储模式: 目标类型={}, 启用回退={}", targetType, enableFallback);
            
            // 设置存储类型
            fileStorageService.setStorageType(targetType);
            
            // 设置回退选项
            fileStorageService.setFallbackToLocal(enableFallback);
            
            // 获取切换后的配置
            Map<String, Object> config = fileStorageService.getStorageConfig();
            
            String message = String.format("存储模式切换成功，当前配置: %s，实际使用: %s", 
                targetType, config.get("actualStorageType"));
            
            config.put("switchMessage", message);
            
            log.info("存储模式切换完成: {}", config);
            return Result.success(config);
        } catch (Exception e) {
            log.error("切换存储模式失败", e);
            return Result.error("切换存储模式失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取存储类型描述
     */
    private String getStorageTypeDescription(String storageType, boolean minioAvailable) {
        switch (storageType) {
            case "minio":
                return minioAvailable ? "正在使用MinIO存储" : "配置为MinIO但服务不可用";
            case "local":
                return "正在使用本地存储";
            default:
                return minioAvailable ? "自动模式：当前使用MinIO" : "自动模式：当前使用本地存储";
        }
    }
}
